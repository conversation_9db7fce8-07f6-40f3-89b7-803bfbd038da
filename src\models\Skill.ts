import mongoose, { Document, Schema } from 'mongoose';

export interface ISkill extends Document {
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'ai' | 'tools';
  level: number; // 1-100
  icon?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const SkillSchema = new Schema<ISkill>({
  name: { type: String, required: true },
  category: {
    type: String,
    enum: ['frontend', 'backend', 'database', 'ai', 'tools'],
    required: true
  },
  level: { type: Number, required: true, min: 1, max: 100 },
  icon: { type: String },
  order: { type: Number, default: 0 }
}, {
  timestamps: true
});

export default mongoose.models.Skill || mongoose.model<ISkill>('Skill', SkillSchema);
