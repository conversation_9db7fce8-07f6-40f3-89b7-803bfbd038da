import mongoose, { Document, Schema } from 'mongoose';

export interface IFAQ extends Document {
  question: {
    en: string;
    ar: string;
  };
  answer: {
    en: string;
    ar: string;
  };
  order: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const FAQSchema = new Schema<IFAQ>({
  question: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  answer: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  order: { type: Number, default: 0 },
  active: { type: Boolean, default: true }
}, {
  timestamps: true
});

export default mongoose.models.FAQ || mongoose.model<IFAQ>('FAQ', FAQSchema);
