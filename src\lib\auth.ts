import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const adminEmail = process.env.ADMIN_EMAIL;
        const adminPassword = process.env.ADMIN_PASSWORD;

        if (!adminEmail || !adminPassword) {
          console.error('Admin credentials not configured');
          return null;
        }

        // Check if email matches
        if (credentials.email !== adminEmail) {
          return null;
        }

        // Check password (you can hash the admin password in env for better security)
        const isValidPassword = await bcrypt.compare(credentials.password, adminPassword);
        
        if (!isValidPassword) {
          // Fallback to plain text comparison for development
          if (credentials.password !== adminPassword) {
            return null;
          }
        }

        return {
          id: '1',
          email: adminEmail,
          name: 'Admin',
          role: 'admin'
        };
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  pages: {
    signIn: '/admin/login'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
      }
      return session;
    }
  }
};
