# <PERSON><PERSON> - Portfolio Website

A modern, bilingual (Arabic & English) full-stack portfolio website built with Next.js, featuring glassmorphism design, smooth animations, and a complete admin dashboard.

## 🚀 Features

### Frontend
- **Bilingual Support**: Full Arabic & English language support with RTL/LTR layouts
- **Modern Design**: Glassmorphism theme with purple, blue, and white gradients
- **Smooth Animations**: Framer Motion animations and transitions
- **Responsive Design**: Mobile-first approach, works on all devices
- **Interactive Elements**: Custom animated cursor, hover effects, microinteractions
- **Skills Visualization**: Interactive radar chart using Chart.js

### Backend
- **Full-Stack Architecture**: Next.js App Router with TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: NextAuth.js for secure admin access
- **Email Integration**: Nodemailer for contact form submissions
- **RESTful API**: Complete CRUD operations for all content

### Admin Dashboard
- **Secure Authentication**: Password-protected admin panel
- **Content Management**: Manage projects, skills, FAQ, and bio
- **Contact Messages**: View and manage contact form submissions
- **Real-time Stats**: Dashboard with content statistics
- **Responsive Admin UI**: Mobile-friendly admin interface

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Animations**: Framer Motion
- **Charts**: Chart.js, React Chart.js 2
- **Backend**: Next.js API Routes, MongoDB, Mongoose
- **Authentication**: NextAuth.js
- **Email**: Nodemailer
- **Internationalization**: next-intl
- **Forms**: React Hook Form
- **Icons**: Lucide React

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/yousef-portfolio.git
   cd yousef-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Edit `.env.local` with your configuration:
   ```env
   # MongoDB
   MONGODB_URI=mongodb://localhost:27017/yousef-portfolio
   
   # NextAuth
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here
   
   # Admin Credentials
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=your-secure-password
   
   # Email Configuration
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   EMAIL_FROM=<EMAIL>
   EMAIL_TO=<EMAIL>
   ```

4. **Seed the database**
   ```bash
   npm run seed
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🗄️ Database Schema

### Projects
- Bilingual title and description
- Category (websites, mobile, management)
- Technologies array
- Image URL
- Live and GitHub URLs
- Featured flag and order

### Skills
- Name and category
- Skill level (1-100)
- Display order

### FAQ
- Bilingual questions and answers
- Active status and order

### Bio
- Bilingual personal information
- Contact details and social links

### Contact Messages
- User submissions from contact form
- Read and replied status

## 🔐 Admin Access

1. Navigate to `/admin/login`
2. Use the credentials from your `.env.local` file
3. Access the admin dashboard at `/admin`

### Admin Features
- **Dashboard**: Overview with statistics
- **Projects**: Add, edit, delete portfolio projects
- **Skills**: Manage skills and proficiency levels
- **FAQ**: Update frequently asked questions
- **Messages**: View contact form submissions
- **Bio**: Edit personal information

## 🌐 Deployment

### Vercel (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Connect your GitHub repository to Vercel
   - Add environment variables in Vercel dashboard
   - Deploy automatically

3. **MongoDB Atlas Setup**
   - Create a MongoDB Atlas cluster
   - Update `MONGODB_URI` in Vercel environment variables
   - Run the seed script after deployment

### Environment Variables for Production
Make sure to set all environment variables in your deployment platform:
- `MONGODB_URI`
- `NEXTAUTH_URL`
- `NEXTAUTH_SECRET`
- `ADMIN_EMAIL`
- `ADMIN_PASSWORD`
- Email configuration variables

## 📱 Pages Structure

```
/                    # Home page (English)
/ar                  # Home page (Arabic)
/about               # About page
/projects            # Projects gallery
/faq                 # FAQ page
/contact             # Contact form
/admin/login         # Admin login
/admin               # Admin dashboard
/admin/projects      # Project management
/admin/skills        # Skills management
/admin/faq           # FAQ management
/admin/messages      # Contact messages
/admin/bio           # Bio editing
```

## 🎨 Customization

### Colors
Edit `tailwind.config.js` to customize the color scheme:
```javascript
colors: {
  primary: { /* your colors */ },
  purple: { /* your colors */ },
}
```

### Content
- Update personal information in the seed script
- Modify translations in `messages/en.json` and `messages/ar.json`
- Replace placeholder images and links

### Styling
- Customize glassmorphism effects in `globals.css`
- Modify animations in component files
- Adjust responsive breakpoints in Tailwind config

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Yousef Khaled Ali**
- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/yourusername](https://linkedin.com/in/yourusername)
- GitHub: [github.com/yourusername](https://github.com/yourusername)

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Framer Motion for smooth animations
- Tailwind CSS for utility-first styling
- MongoDB for the database solution
- All open-source contributors

---

Made with ❤️ by Yousef Khaled Ali
