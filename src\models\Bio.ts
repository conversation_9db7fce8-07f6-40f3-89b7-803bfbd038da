import mongoose, { Document, Schema } from 'mongoose';

export interface IBio extends Document {
  name: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  bio: {
    en: string;
    ar: string;
  };
  location: {
    en: string;
    ar: string;
  };
  email: string;
  phone?: string;
  linkedin?: string;
  github?: string;
  twitter?: string;
  avatar?: string;
  resume?: string;
  createdAt: Date;
  updatedAt: Date;
}

const BioSchema = new Schema<IBio>({
  name: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  title: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  bio: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  location: {
    en: { type: String, required: true },
    ar: { type: String, required: true }
  },
  email: { type: String, required: true },
  phone: { type: String },
  linkedin: { type: String },
  github: { type: String },
  twitter: { type: String },
  avatar: { type: String },
  resume: { type: String }
}, {
  timestamps: true
});

export default mongoose.models.Bio || mongoose.model<IBio>('Bio', BioSchema);
