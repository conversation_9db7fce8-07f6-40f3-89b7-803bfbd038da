const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Import models
const Project = require('../src/models/Project').default;
const Skill = require('../src/models/Skill').default;
const FAQ = require('../src/models/FAQ').default;
const Bio = require('../src/models/Bio').default;

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

const sampleProjects = [
  {
    title: {
      en: 'E-Commerce Platform',
      ar: 'منصة التجارة الإلكترونية'
    },
    description: {
      en: 'A full-stack e-commerce platform built with Next.js, featuring user authentication, payment integration, and admin dashboard.',
      ar: 'منصة تجارة إلكترونية متكاملة مبنية بـ Next.js، تتضمن مصادقة المستخدمين وتكامل الدفع ولوحة إدارة.'
    },
    category: 'websites',
    technologies: ['Next.js', 'React', 'Node.js', 'MongoDB', 'Stripe', 'Tailwind CSS'],
    image: 'https://via.placeholder.com/600x400/6366f1/ffffff?text=E-Commerce',
    liveUrl: 'https://example-ecommerce.com',
    githubUrl: 'https://github.com/yourusername/ecommerce',
    featured: true,
    order: 1
  },
  {
    title: {
      en: 'Task Management App',
      ar: 'تطبيق إدارة المهام'
    },
    description: {
      en: 'A React Native mobile app for task management with real-time synchronization and team collaboration features.',
      ar: 'تطبيق جوال بـ React Native لإدارة المهام مع المزامنة الفورية وميزات التعاون الجماعي.'
    },
    category: 'mobile',
    technologies: ['React Native', 'Firebase', 'Redux', 'TypeScript'],
    image: 'https://via.placeholder.com/600x400/8b5cf6/ffffff?text=Task+App',
    liveUrl: 'https://play.google.com/store/apps/details?id=com.example.taskapp',
    githubUrl: 'https://github.com/yourusername/task-app',
    featured: true,
    order: 2
  },
  {
    title: {
      en: 'Hospital Management System',
      ar: 'نظام إدارة المستشفيات'
    },
    description: {
      en: 'A comprehensive hospital management system with patient records, appointment scheduling, and billing modules.',
      ar: 'نظام إدارة مستشفيات شامل مع سجلات المرضى وجدولة المواعيد ووحدات الفوترة.'
    },
    category: 'management',
    technologies: ['Vue.js', 'Laravel', 'MySQL', 'PHP', 'Bootstrap'],
    image: 'https://via.placeholder.com/600x400/06b6d4/ffffff?text=Hospital+System',
    liveUrl: 'https://hospital-demo.com',
    githubUrl: 'https://github.com/yourusername/hospital-system',
    featured: false,
    order: 3
  }
];

const sampleSkills = [
  { name: 'HTML', category: 'frontend', level: 95, order: 1 },
  { name: 'CSS', category: 'frontend', level: 90, order: 2 },
  { name: 'JavaScript', category: 'frontend', level: 92, order: 3 },
  { name: 'TypeScript', category: 'frontend', level: 88, order: 4 },
  { name: 'React', category: 'frontend', level: 90, order: 5 },
  { name: 'Next.js', category: 'frontend', level: 85, order: 6 },
  { name: 'Tailwind CSS', category: 'frontend', level: 88, order: 7 },
  { name: 'Node.js', category: 'backend', level: 85, order: 8 },
  { name: 'Express.js', category: 'backend', level: 82, order: 9 },
  { name: 'Python', category: 'backend', level: 80, order: 10 },
  { name: 'MongoDB', category: 'database', level: 85, order: 11 },
  { name: 'MySQL', category: 'database', level: 78, order: 12 },
  { name: 'TensorFlow', category: 'ai', level: 75, order: 13 },
  { name: 'OpenAI API', category: 'ai', level: 80, order: 14 },
  { name: 'Git', category: 'tools', level: 90, order: 15 },
  { name: 'Docker', category: 'tools', level: 70, order: 16 }
];

const sampleFAQs = [
  {
    question: {
      en: 'What technologies do you specialize in?',
      ar: 'ما هي التقنيات التي تتخصص فيها؟'
    },
    answer: {
      en: 'I specialize in full-stack web development using modern technologies like React, Next.js, Node.js, and MongoDB. I also have experience with AI technologies including Python, TensorFlow, and OpenAI API.',
      ar: 'أتخصص في تطوير الويب الكامل باستخدام التقنيات الحديثة مثل React و Next.js و Node.js و MongoDB. لدي أيضاً خبرة في تقنيات الذكاء الاصطناعي بما في ذلك Python و TensorFlow و OpenAI API.'
    },
    order: 1,
    active: true
  },
  {
    question: {
      en: 'How long does it take to complete a project?',
      ar: 'كم من الوقت يستغرق إنجاز المشروع؟'
    },
    answer: {
      en: 'Project timelines vary depending on complexity and requirements. A simple website might take 2-4 weeks, while a complex web application could take 2-6 months. I always provide detailed timelines during the planning phase.',
      ar: 'تختلف مواعيد المشاريع حسب التعقيد والمتطلبات. قد يستغرق الموقع البسيط 2-4 أسابيع، بينما قد يستغرق تطبيق الويب المعقد 2-6 أشهر. أقدم دائماً جداول زمنية مفصلة خلال مرحلة التخطيط.'
    },
    order: 2,
    active: true
  },
  {
    question: {
      en: 'Do you provide ongoing support and maintenance?',
      ar: 'هل تقدم الدعم والصيانة المستمرة؟'
    },
    answer: {
      en: 'Yes, I offer ongoing support and maintenance services. This includes bug fixes, security updates, performance optimization, and feature enhancements. Support packages can be customized based on your needs.',
      ar: 'نعم، أقدم خدمات الدعم والصيانة المستمرة. يشمل ذلك إصلاح الأخطاء وتحديثات الأمان وتحسين الأداء وتحسين الميزات. يمكن تخصيص حزم الدعم حسب احتياجاتك.'
    },
    order: 3,
    active: true
  }
];

const sampleBio = {
  name: {
    en: 'Yousef Khaled Ali',
    ar: 'يوسف خالد علي'
  },
  title: {
    en: 'Full Stack Web Developer & AI Enthusiast',
    ar: 'مطوّر ويب كامل ومهتم بالذكاء الاصطناعي'
  },
  bio: {
    en: 'Full Stack Web Developer passionate about creating integrated software solutions that combine modern design and high performance, while leveraging AI technologies to craft unique digital experiences.',
    ar: 'مطوّر ويب كامل (Full Stack) ومهتم بابتكار حلول برمجية متكاملة تجمع بين التصميم العصري والأداء العالي، مع دمج تقنيات الذكاء الاصطناعي لابتكار تجارب رقمية فريدة.'
  },
  location: {
    en: 'Cairo, Egypt',
    ar: 'القاهرة، مصر'
  },
  email: '<EMAIL>',
  phone: '+20 ************',
  linkedin: 'https://linkedin.com/in/yourusername',
  github: 'https://github.com/yourusername',
  twitter: 'https://twitter.com/yourusername'
};

async function seedDatabase() {
  try {
    await connectDB();

    // Clear existing data
    console.log('Clearing existing data...');
    await Promise.all([
      Project.deleteMany({}),
      Skill.deleteMany({}),
      FAQ.deleteMany({}),
      Bio.deleteMany({})
    ]);

    // Seed projects
    console.log('Seeding projects...');
    await Project.insertMany(sampleProjects);

    // Seed skills
    console.log('Seeding skills...');
    await Skill.insertMany(sampleSkills);

    // Seed FAQs
    console.log('Seeding FAQs...');
    await FAQ.insertMany(sampleFAQs);

    // Seed bio
    console.log('Seeding bio...');
    await Bio.create(sampleBio);

    console.log('Database seeded successfully!');
    console.log(`- ${sampleProjects.length} projects`);
    console.log(`- ${sampleSkills.length} skills`);
    console.log(`- ${sampleFAQs.length} FAQs`);
    console.log('- 1 bio entry');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the seed function
seedDatabase();
