import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/mongodb';
import Bio from '@/models/Bio';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    await dbConnect();
    const bio = await Bio.findOne();
    return NextResponse.json(bio);
  } catch (error) {
    console.error('Error fetching bio:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();
    
    await dbConnect();
    
    // Check if bio already exists
    const existingBio = await Bio.findOne();
    
    if (existingBio) {
      // Update existing bio
      const updatedBio = await Bio.findByIdAndUpdate(
        existingBio._id,
        data,
        { new: true, runValidators: true }
      );
      return NextResponse.json(updatedBio);
    } else {
      // Create new bio
      const bio = new Bio(data);
      await bio.save();
      return NextResponse.json(bio, { status: 201 });
    }
  } catch (error) {
    console.error('Error saving bio:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
