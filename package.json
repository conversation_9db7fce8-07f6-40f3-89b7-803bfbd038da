{"name": "yousef-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed.js"}, "dependencies": {"@next-auth/mongodb-adapter": "^1.1.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "chart.js": "^4.4.0", "eslint": "^8", "eslint-config-next": "14.0.4", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "next": "14.0.4", "next-auth": "^4.24.5", "next-intl": "^3.4.0", "nodemailer": "^6.9.7", "postcss": "^8", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-hook-form": "^7.48.2", "tailwindcss": "^3.3.0", "typescript": "^5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.14"}}