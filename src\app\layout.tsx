import './globals.css';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '<PERSON><PERSON> - Full Stack Developer',
  description: 'Full Stack Web Developer & AI Enthusiast creating integrated software solutions',
  keywords: 'Full Stack Developer, Web Developer, AI, React, Next.js, Node.js, MongoDB',
  authors: [{ name: '<PERSON><PERSON>' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html>
      <body className="animated-bg">
        {children}
      </body>
    </html>
  );
}
