'use client';

import { motion } from 'framer-motion';
import { useTranslations, useLocale } from 'next-intl';
import { Github, Linkedin, Mail, Heart } from 'lucide-react';
import Link from 'next/link';

export default function Footer() {
  const t = useTranslations();
  const locale = useLocale();

  const socialLinks = [
    { icon: Github, href: 'https://github.com/yourusername', label: 'GitHub' },
    { icon: Linkedin, href: 'https://linkedin.com/in/yourusername', label: 'LinkedIn' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },
  ];

  return (
    <footer className="relative mt-20 py-12 backdrop-blur-md bg-white/5 border-t border-white/10">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="text-center md:text-left">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-4">
              {locale === 'ar' ? 'يوسف خالد علي' : 'Yousef Khaled Ali'}
            </h3>
            <p className="text-gray-300 text-sm">
              {locale === 'ar' 
                ? 'مطوّر ويب كامل ومهتم بالذكاء الاصطناعي'
                : 'Full Stack Web Developer & AI Enthusiast'
              }
            </p>
          </div>

          {/* Quick Links */}
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-4 text-white">
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h4>
            <div className="flex flex-col gap-2">
              <Link 
                href={`/${locale}/about`} 
                className="text-gray-300 hover:text-purple-400 transition-colors duration-300"
              >
                {t('nav.about')}
              </Link>
              <Link 
                href={`/${locale}/projects`} 
                className="text-gray-300 hover:text-purple-400 transition-colors duration-300"
              >
                {t('nav.projects')}
              </Link>
              <Link 
                href={`/${locale}/contact`} 
                className="text-gray-300 hover:text-purple-400 transition-colors duration-300"
              >
                {t('nav.contact')}
              </Link>
            </div>
          </div>

          {/* Social Links */}
          <div className="text-center md:text-right">
            <h4 className="text-lg font-semibold mb-4 text-white">
              {locale === 'ar' ? 'تواصل معي' : 'Connect With Me'}
            </h4>
            <div className="flex justify-center md:justify-end gap-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 rounded-full backdrop-blur-md bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </div>
        </div>

        {/* Copyright */}
        <motion.div 
          className="mt-8 pt-8 border-t border-white/10 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-gray-400 text-sm flex items-center justify-center gap-2">
            {locale === 'ar' ? 'صُنع بـ' : 'Made with'} 
            <Heart className="w-4 h-4 text-red-400" /> 
            {locale === 'ar' ? 'بواسطة يوسف خالد علي' : 'by Yousef Khaled Ali'}
          </p>
          <p className="text-gray-500 text-xs mt-2">
            © {new Date().getFullYear()} {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved'}
          </p>
        </motion.div>
      </div>
    </footer>
  );
}
