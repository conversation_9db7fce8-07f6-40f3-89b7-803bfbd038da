'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { signOut } from 'next-auth/react';
import { 
  LayoutDashboard, 
  FolderOpen, 
  Settings, 
  HelpCircle, 
  Mail, 
  User, 
  LogOut,
  BarChart3,
  MessageSquare
} from 'lucide-react';
import GlassCard from '@/components/ui/GlassCard';

interface Stats {
  projects: number;
  skills: number;
  faqs: number;
  messages: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({ projects: 0, skills: 0, faqs: 0, messages: 0 });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [projectsRes, skillsRes, faqsRes, messagesRes] = await Promise.all([
          fetch('/api/projects'),
          fetch('/api/skills'),
          fetch('/api/faq'),
          fetch('/api/contact')
        ]);

        const [projects, skills, faqs, messages] = await Promise.all([
          projectsRes.json(),
          skillsRes.json(),
          faqsRes.json(),
          messagesRes.json()
        ]);

        setStats({
          projects: projects.length || 0,
          skills: skills.length || 0,
          faqs: faqs.length || 0,
          messages: messages.length || 0
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const menuItems = [
    {
      title: 'Projects',
      description: 'Manage your portfolio projects',
      icon: FolderOpen,
      href: '/admin/projects',
      count: stats.projects,
      color: 'from-purple-500 to-blue-500'
    },
    {
      title: 'Skills',
      description: 'Update your skills and expertise',
      icon: BarChart3,
      href: '/admin/skills',
      count: stats.skills,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'FAQ',
      description: 'Manage frequently asked questions',
      icon: HelpCircle,
      href: '/admin/faq',
      count: stats.faqs,
      color: 'from-green-500 to-teal-500'
    },
    {
      title: 'Messages',
      description: 'View contact form submissions',
      icon: MessageSquare,
      href: '/admin/messages',
      count: stats.messages,
      color: 'from-orange-500 to-red-500'
    },
    {
      title: 'Bio',
      description: 'Edit your personal information',
      icon: User,
      href: '/admin/bio',
      count: null,
      color: 'from-pink-500 to-purple-500'
    }
  ];

  const handleSignOut = () => {
    signOut({ callbackUrl: '/admin/login' });
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-gray-400 mt-2">Manage your portfolio content</p>
          </div>
          
          <motion.button
            onClick={handleSignOut}
            className="flex items-center gap-2 px-4 py-2 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 hover:bg-red-500/30 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <LogOut className="w-4 h-4" />
            Sign Out
          </motion.button>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Projects</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.projects}
                </p>
              </div>
              <FolderOpen className="w-8 h-8 text-purple-400" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Skills</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.skills}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-400" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">FAQ Items</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.faqs}
                </p>
              </div>
              <HelpCircle className="w-8 h-8 text-green-400" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Messages</p>
                <p className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.messages}
                </p>
              </div>
              <MessageSquare className="w-8 h-8 text-orange-400" />
            </div>
          </GlassCard>
        </motion.div>

        {/* Menu Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {menuItems.map((item, index) => (
            <motion.a
              key={item.title}
              href={item.href}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
            >
              <GlassCard className="p-6 h-full hover:scale-105 transition-transform duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${item.color}`}>
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  {item.count !== null && (
                    <span className="px-2 py-1 bg-white/10 rounded-full text-sm text-gray-300">
                      {loading ? '...' : item.count}
                    </span>
                  )}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{item.title}</h3>
                <p className="text-gray-400 text-sm">{item.description}</p>
              </GlassCard>
            </motion.a>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <GlassCard className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <motion.a
                href="/admin/projects"
                className="flex items-center gap-3 p-4 bg-purple-500/20 border border-purple-500/30 rounded-lg hover:bg-purple-500/30 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
              >
                <FolderOpen className="w-5 h-5 text-purple-400" />
                <span className="text-white">Add New Project</span>
              </motion.a>

              <motion.a
                href="/admin/skills"
                className="flex items-center gap-3 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
              >
                <BarChart3 className="w-5 h-5 text-blue-400" />
                <span className="text-white">Update Skills</span>
              </motion.a>

              <motion.a
                href="/admin/messages"
                className="flex items-center gap-3 p-4 bg-orange-500/20 border border-orange-500/30 rounded-lg hover:bg-orange-500/30 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
              >
                <MessageSquare className="w-5 h-5 text-orange-400" />
                <span className="text-white">Check Messages</span>
              </motion.a>
            </div>
          </GlassCard>
        </motion.div>
      </div>
    </div>
  );
}
